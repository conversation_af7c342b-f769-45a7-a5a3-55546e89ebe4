Finetuning started at: Thu Aug 21 09:49:40 AM CEST 2025
Configuration:
  Model: resunet_cbam
  Pretrained: ./outputs/resunet_cbam_pretrain_20250820_185256/best_model.pth
  Batch Size: 7
  Image Size: 1024
  Epochs: 100
  Learning Rate: 1e-5
  Freeze Epochs: 15
  GPUs: 2

Starting finetuning...
============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 19,491
Val samples:   2,539
Test samples:  653
Total samples: 22,683
============================================================

TRAIN Dataset Summary:
  Total images found: 19491
  Valid image-mask pairs: 19491
  Images without masks: 0
  Final train dataset size: 19491

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
Loading pretrained model from ./outputs/resunet_cbam_pretrain_20250820_185256/best_model.pth
Loaded pretrained model with best IoU: 0.8985806636430405
Froze 2/4 encoder blocks in resunet_cbam

TRAIN Dataset Summary:
  Total images found: 19491
  Valid image-mask pairs: 19491
  Images without masks: 0
  Final train dataset size: 19491

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_small
Image size: 1024x1024
------------------------------------------------------------
Train samples: 19,491
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 22,683
============================================================

Loading pretrained model from ./outputs/resunet_cbam_pretrain_20250820_185256/best_model.pth
Loaded pretrained model with best IoU: 0.8985806636430405
Froze 2/4 encoder blocks in resunet_cbam

Epoch 0 Training:   0%|          | 0/1392 [00:00<?, ?it/s]
Epoch 0 Training:   0%|          | 0/1392 [00:29<?, ?it/s, loss=0.00697, iou=0.957, dice=0.978]
Epoch 0 Training:   0%|          | 1/1392 [00:29<11:21:38, 29.40s/it, loss=0.00697, iou=0.957, dice=0.978]
Epoch 0 Training:   0%|          | 1/1392 [00:30<11:21:38, 29.40s/it, loss=0.0309, iou=0.961, dice=0.98]  
Epoch 0 Training:   0%|          | 2/1392 [00:30<5:00:46, 12.98s/it, loss=0.0309, iou=0.961, dice=0.98] 
Epoch 0 Training:   0%|          | 2/1392 [00:32<5:00:46, 12.98s/it, loss=0.142, iou=0.88, dice=0.936] 
Epoch 0 Training:   0%|          | 3/1392 [00:32<2:58:56,  7.73s/it, loss=0.142, iou=0.88, dice=0.936]
Epoch 0 Training:   0%|          | 3/1392 [00:33<2:58:56,  7.73s/it, loss=-.00531, iou=0.974, dice=0.987]
Epoch 0 Training:   0%|          | 4/1392 [00:33<2:02:07,  5.28s/it, loss=-.00531, iou=0.974, dice=0.987]
Epoch 0 Training:   0%|          | 4/1392 [00:35<2:02:07,  5.28s/it, loss=-.0363, iou=0.981, dice=0.991] 
Epoch 0 Training:   0%|          | 5/1392 [00:35<1:30:05,  3.90s/it, loss=-.0363, iou=0.981, dice=0.991]
Epoch 0 Training:   0%|          | 5/1392 [00:36<1:30:05,  3.90s/it, loss=-.0201, iou=0.969, dice=0.984]
Epoch 0 Training:   0%|          | 6/1392 [00:36<1:11:24,  3.09s/it, loss=-.0201, iou=0.969, dice=0.984]
Epoch 0 Training:   0%|          | 6/1392 [00:38<1:11:24,  3.09s/it, loss=0.0953, iou=0.905, dice=0.95] 
Epoch 0 Training:   1%|          | 7/1392 [00:38<59:21,  2.57s/it, loss=0.0953, iou=0.905, dice=0.95]  
Epoch 0 Training:   1%|          | 7/1392 [00:40<59:21,  2.57s/it, loss=0.048, iou=0.933, dice=0.965]
Epoch 0 Training:   1%|          | 8/1392 [00:40<52:47,  2.29s/it, loss=0.048, iou=0.933, dice=0.965]
Epoch 0 Training:   1%|          | 8/1392 [00:41<52:47,  2.29s/it, loss=0.00801, iou=0.967, dice=0.983]
Epoch 0 Training:   1%|          | 9/1392 [00:41<47:27,  2.06s/it, loss=0.00801, iou=0.967, dice=0.983]
Epoch 0 Training:   1%|          | 9/1392 [00:43<47:27,  2.06s/it, loss=0.059, iou=0.906, dice=0.951]  
Epoch 0 Training:   1%|          | 10/1392 [00:43<43:43,  1.90s/it, loss=0.059, iou=0.906, dice=0.951]
Epoch 0 Training:   1%|          | 10/1392 [00:44<43:43,  1.90s/it, loss=0.0738, iou=0.919, dice=0.958]
Epoch 0 Training:   1%|          | 11/1392 [00:44<40:54,  1.78s/it, loss=0.0738, iou=0.919, dice=0.958]
Epoch 0 Training:   1%|          | 11/1392 [00:48<1:40:33,  4.37s/it, loss=0.0738, iou=0.919, dice=0.958]
[rank0]:[W821 09:50:43.398016825 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0821 09:50:44.396198 2494083 torch/multiprocessing/spawn.py:169] Terminating process 2494222 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1468, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1463, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 1 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1292, in train
    train_loss, train_metrics, loss_components = train_epoch(
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 492, in train_epoch
    scaler.scale(loss).backward()
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/autograd/__init__.py", line 353, in backward
    _engine_run_backward(
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 448.00 MiB. GPU 1 has a total capacity of 44.39 GiB of which 425.31 MiB is free. Including non-PyTorch memory, this process has 43.96 GiB memory in use. Of the allocated memory 42.56 GiB is allocated by PyTorch, and 764.11 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

